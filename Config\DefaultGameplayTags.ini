

[/Script/GameplayTags.GameplayTagsSettings]
ImportTagsFromConfig=False
WarnOnInvalidTags=True
FastReplication=False
InvalidTagCharacters="\"\',"
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/ItemsSystem/DataTables/DT_GameplayTags_Items.DT_GameplayTags_Items
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/BehaviorSystem/DataTables/DT_GameplayTags_BehaviorTree.DT_GameplayTags_BehaviorTree
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/DamageSystem/DataTables/DT_GameplayTags_DamageSystem.DT_GameplayTags_DamageSystem
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/CharacterSystem/DataTables/DT_GameplayTags_InteractionSystem.DT_GameplayTags_InteractionSystem
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/AbilitySystem/DataTables/DT_GameplayTags_AbilitySystem.DT_GameplayTags_AbilitySystem
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/BehaviorSystem/DataTables/DT_GameplayTags_Factions.DT_GameplayTags_Factions
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Game/DataTables/DT_GameplayTags_Interactions_TD.DT_GameplayTags_Interactions_TD
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Systems/CharacterSystem/DataTables/DT_GameplayTags_Attributes_Character.DT_GameplayTags_Attributes_Character
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Game/DataTables/DT_GameplayTags_Items_TD.DT_GameplayTags_Items_TD
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Game/DataTables/DT_GameplayTags_Narrative_TD.DT_GameplayTags_Narrative_TD
+GameplayTagTableList=/Game/EasyTopDown/Blueprints/Game/DataTables/DT_GameplayTags_Attributes_Skills.DT_GameplayTags_Attributes_Skills
NumBitsForContainerSize=6
NetIndexFirstBitSegment=16

